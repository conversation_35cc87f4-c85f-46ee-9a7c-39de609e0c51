# ninja log v5
8	77	0	F:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/.cxx/Debug/6f6s2k27/arm64-v8a/CMakeFiles/cmake.verify_globs	f7d4fe1e264d2c2e
11	4928	7751303975275358	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp.o	1ae7513ecd868b51
64	5596	7751303981731548	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Tools/ReanimatedJSIUtils.cpp.o	9e3871a3cd153608
36	5880	7751303984901034	src/main/cpp/worklets/CMakeFiles/worklets.dir/559af093e8f85ca44b37b2aa4acde804/react-native-reanimated/Common/cpp/worklets/Tools/JSLogger.cpp.o	f595a01ea476f51d
54	6160	7751303987555390	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Tools/JSScheduler.cpp.o	54dec51e7b6762b5
82	6404	7751303989615566	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Tools/WorkletEventHandler.cpp.o	b8518b5ddf9a5cc
19	6517	7751303990387277	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Tools/AsyncQueue.cpp.o	56cfe647947944e9
74	6961	7751303994945015	src/main/cpp/worklets/CMakeFiles/worklets.dir/6ee4d8ed6afcabfa3541557a7218e0cc/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp.o	c7037fa6f9b524a0
27	7070	7751303995677171	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Tools/JSISerializer.cpp.o	cd7f53348e2b541c
4	8368	7751304008812851	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/SharedItems/Shareables.cpp.o	3de7f036c9bb89fc
4978	9278	7751304018502453	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Tools/UIScheduler.cpp.o	940ac9df4c638af3
7070	9343	7751304019592625	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/PlatformLogger.cpp.o	b7e5ee6bf5d74893
5881	11121	7751304036830433	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/WorkletRuntime/ReanimatedRuntime.cpp.o	bb87b1d054bb1e5f
5607	12579	7751304051366290	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/WorkletRuntime/ReanimatedHermesRuntime.cpp.o	efb803165a12c123
45	12677	7751304052078789	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Tools/ReanimatedVersion.cpp.o	4fdfaeb6333feba7
6161	13329	7751304059191407	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp.o	21a0df886760065
6518	15448	7751304079909903	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp.o	81d2199b277f133d
6428	19904	7751304120553383	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/AndroidUIScheduler.cpp.o	260003390fdd3251
12583	20678	7751304132347624	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/b3753698fca1a6c32856249ade94ea38/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp.o	164d74970a08287
15460	22978	7751304154611736	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/6ee4d8ed6afcabfa3541557a7218e0cc/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp.o	691ac5e2ba439e87
7007	25384	7751304177351137	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsOnLoad.cpp.o	6dec1c9bd3caee61
12698	27165	7751304194141945	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp.o	ba2b0aa4f5eccf5e
9295	27557	7751304199006586	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp.o	3c7708c602700782
11131	28762	7751304207816556	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp.o	571fd8517c9290ad
8425	30402	7751304226403265	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsModule.cpp.o	bb35d12907c1d08d
20690	30700	7751304230792242	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/NativeModules/WorkletsModuleProxySpec.cpp.o	d5bf51cc5f64ee98
28834	30923	7751304234999261	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/reanimated/Tools/FeaturesConfig.cpp.o	3f358f2ee566d19e
13329	32985	7751304254251191	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/6ee4d8ed6afcabfa3541557a7218e0cc/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp.o	fcc5c8974f9eb8da
23038	33564	7751304259802835	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Registries/EventHandlerRegistry.cpp.o	7256c3388359a80
27570	36426	7751304289045722	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/6ee4d8ed6afcabfa3541557a7218e0cc/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp.o	18c98965de71e73f
30741	37934	7751304305459900	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/6ee4d8ed6afcabfa3541557a7218e0cc/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp.o	a7712fb54994a332
9343	38208	7751304307085228	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/6ee4d8ed6afcabfa3541557a7218e0cc/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp.o	638164baf2a604e4
20035	41257	7751304338586041	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/reanimated/Fabric/PropsRegistry.cpp.o	a3fd93c63d883a09
25396	42439	7751304349481649	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp.o	cde89049d0525836
42452	43145	7751304356789123	../../../../build/intermediates/cxx/Debug/6f6s2k27/obj/arm64-v8a/libworklets.so	77abde51da2e2bc8
30421	48864	7751304412369321	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/6ee4d8ed6afcabfa3541557a7218e0cc/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp.o	a4163a0874d9b2bf
33054	49017	7751304414764734	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/JNIHelper.cpp.o	47a6301cab462acc
33617	52180	7751304446981868	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/LayoutAnimations.cpp.o	c64ba9ca07c662b3
27209	58577	7751304511233687	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/6ee4d8ed6afcabfa3541557a7218e0cc/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp.o	2e0c0a264c6c55bf
36450	59690	7751304523026407	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/OnLoad.cpp.o	ce02c36681a6ea1
30924	60471	7751304529124801	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/NativeProxy.cpp.o	55d800df731b1efc
60472	60961	7751304535343328	../../../../build/intermediates/cxx/Debug/6f6s2k27/obj/arm64-v8a/libreanimated.so	c1f5fa337f053947
2	74	0	F:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/.cxx/Debug/6f6s2k27/arm64-v8a/CMakeFiles/cmake.verify_globs	f7d4fe1e264d2c2e
545	4835	0	F:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/.cxx/Debug/6f6s2k27/arm64-v8a/CMakeFiles/cmake.verify_globs	f7d4fe1e264d2c2e
3	4442	0	F:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/.cxx/Debug/6f6s2k27/arm64-v8a/CMakeFiles/cmake.verify_globs	f7d4fe1e264d2c2e
644	4136	0	F:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/.cxx/Debug/6f6s2k27/arm64-v8a/CMakeFiles/cmake.verify_globs	f7d4fe1e264d2c2e
357	3221	0	F:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/.cxx/Debug/6f6s2k27/arm64-v8a/CMakeFiles/cmake.verify_globs	f7d4fe1e264d2c2e
2	51	0	F:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/.cxx/Debug/6f6s2k27/arm64-v8a/CMakeFiles/cmake.verify_globs	f7d4fe1e264d2c2e
